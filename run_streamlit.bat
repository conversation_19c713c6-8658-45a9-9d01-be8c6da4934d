@echo off
echo 🚀 Starting Advanced Plotly + Dash Visualizer...
echo.
echo 📁 Looking for JSON files...

if exist "Bush_George.json" (
    echo ✅ Found Bush_George.json
    echo 🌐 Opening at: http://localhost:8050
    python plotly_dash_visualizer.py Bush_George.json
) else if exist "*.json" (
    echo ✅ Found JSON files
    echo 🌐 Opening at: http://localhost:8050
    python plotly_dash_visualizer.py *.json
) else (
    echo ❌ No JSON files found
    echo 💡 First run: python testdb.py "FirstName" "LastName"
    pause
)
