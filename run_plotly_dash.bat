@echo off
echo 🚀 Starting Advanced Plotly + Dash Visualizer...
echo.
echo 📁 Looking for JSON files...

if exist "Bush_George.json" (
    echo ✅ Found Bush_George.json
    echo 🌐 Opening at: http://localhost:8050
    echo 💡 Click on nodes to see detailed information!
    echo 📊 Scroll down for analytics charts!
    python plotly_dash_visualizer.py Bush_George.json
) else (
    for %%f in (*.json) do (
        echo ✅ Found %%f
        echo 🌐 Opening at: http://localhost:8050
        echo 💡 Click on nodes to see detailed information!
        echo 📊 Scroll down for analytics charts!
        python plotly_dash_visualizer.py "%%f"
        goto :found
    )
    echo ❌ No JSON files found
    echo 💡 First run: python testdb.py "FirstName" "LastName"
    pause
)
:found
