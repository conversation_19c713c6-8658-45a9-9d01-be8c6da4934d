
"""
Interactive Data Visualizer for JSON Data
Creates a web-based interactive visualization where clicking on elements shows detailed information.
"""

import json
import sys
import dash
from dash import dcc, html, Input, Output, callback
import plotly.graph_objects as go
from datetime import datetime
from typing import Dict, List, Any


class InteractiveDataVisualizer:
    def __init__(self, json_file_path: str):
        """Initialize the visualizer with JSON data."""
        with open(json_file_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        
        self.app = dash.Dash(__name__)
        self.setup_layout()
        self.setup_callbacks()
    
    def format_datetime(self, dt_str):
        """Format datetime strings for display."""
        if not dt_str:
            return "N/A"
        try:
            if isinstance(dt_str, str):
                dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
                return dt.strftime("%d-%b-%Y %H:%M")
            return str(dt_str)
        except:
            return str(dt_str)
    
    def create_hierarchy_tree(self):
        """Create a hierarchical tree visualization."""
        fig = go.Figure()
        
        # Extract user info
        user = self.data.get('User', {})
        user_name = f"{user.get('Firstname', '')} {user.get('Lastname', '')}"
        
        # Create nodes and edges for the tree
        nodes = []
        edges = []
        node_info = {}
        
        # Root node - User
        user_id = f"user_{user.get('UserID', 0)}"
        nodes.append({
            'id': user_id,
            'label': f"User: {user_name}",
            'level': 0,
            'type': 'user',
            'data': user
        })
        node_info[user_id] = user
        
        # Appointments
        appointments = self.data.get('Appointments', [])
        for i, appt_data in enumerate(appointments):
            appt = appt_data.get('Appointment', {})
            appt_id = f"appt_{appt.get('AppointmentId', i)}"
            appt_no = appt.get('AppNo', 'N/A')
            appt_dt = self.format_datetime(appt.get('AppointmentDateTime'))
            
            nodes.append({
                'id': appt_id,
                'label': f"Appointment {appt_no}",
                'level': 1,
                'type': 'appointment',
                'data': appt_data
            })
            node_info[appt_id] = appt_data
            edges.append((user_id, appt_id))
            
            # Workflows
            workflows = appt_data.get('Workflows', [])
            for j, workflow in enumerate(workflows):
                wf_id = f"wf_{appt_id}_{j}"
                wf_appt_id = workflow.get('AppointmentId', 'N/A')
                
                nodes.append({
                    'id': wf_id,
                    'label': f"Workflow {wf_appt_id}",
                    'level': 2,
                    'type': 'workflow',
                    'data': workflow
                })
                node_info[wf_id] = workflow
                edges.append((appt_id, wf_id))
        
        return self.create_network_plot(nodes, edges, node_info)
    
    def create_network_plot(self, nodes, edges, node_info):
        """Create a network plot using Plotly."""
        # Calculate positions for hierarchical layout
        levels = {}
        for node in nodes:
            level = node['level']
            if level not in levels:
                levels[level] = []
            levels[level].append(node)
        
        # Position nodes
        x_positions = []
        y_positions = []
        node_colors = []
        node_sizes = []
        hover_texts = []
        node_ids = []
        
        color_map = {
            'user': '#FF6B6B',
            'appointment': '#4ECDC4',
            'workflow': '#45B7D1',
            'audit': '#96CEB4',
            'kiosk': '#FFEAA7'
        }
        
        for level, level_nodes in levels.items():
            y = -level * 2  # Vertical spacing
            x_spacing = 4 if len(level_nodes) > 1 else 0
            x_start = -x_spacing * (len(level_nodes) - 1) / 2
            
            for i, node in enumerate(level_nodes):
                x = x_start + i * x_spacing
                x_positions.append(x)
                y_positions.append(y)
                node_colors.append(color_map.get(node['type'], '#95A5A6'))
                node_sizes.append(30 if node['type'] == 'user' else 20)
                
                # Create hover text
                hover_text = f"<b>{node['label']}</b><br>"
                if node['type'] == 'user':
                    data = node['data']
                    hover_text += f"UserID: {data.get('UserID', 'N/A')}<br>"
                    hover_text += f"Email: {data.get('Email', 'N/A')}"
                elif node['type'] == 'appointment':
                    appt = node['data'].get('Appointment', {})
                    hover_text += f"AppNo: {appt.get('AppNo', 'N/A')}<br>"
                    hover_text += f"DateTime: {self.format_datetime(appt.get('AppointmentDateTime'))}"
                elif node['type'] == 'workflow':
                    hover_text += f"AppointmentId: {node['data'].get('AppointmentId', 'N/A')}<br>"
                    hover_text += f"Audits: {len(node['data'].get('WorkflowAuditLog', []))}<br>"
                    hover_text += f"Kiosks: {len(node['data'].get('KioskWorkflow', []))}"
                
                hover_texts.append(hover_text)
                node_ids.append(node['id'])
        
        # Create edges
        edge_x = []
        edge_y = []
        for edge in edges:
            start_idx = node_ids.index(edge[0])
            end_idx = node_ids.index(edge[1])
            edge_x.extend([x_positions[start_idx], x_positions[end_idx], None])
            edge_y.extend([y_positions[start_idx], y_positions[end_idx], None])
        
        # Create the plot
        fig = go.Figure()
        
        # Add edges
        fig.add_trace(go.Scatter(
            x=edge_x, y=edge_y,
            mode='lines',
            line=dict(width=2, color='#BDC3C7'),
            hoverinfo='none',
            showlegend=False
        ))
        
        # Add nodes
        fig.add_trace(go.Scatter(
            x=x_positions, y=y_positions,
            mode='markers+text',
            marker=dict(
                size=node_sizes,
                color=node_colors,
                line=dict(width=2, color='white')
            ),
            text=[node['label'] for node in nodes],
            textposition="bottom center",
            hovertext=hover_texts,
            hoverinfo='text',
            customdata=node_ids,
            showlegend=False
        ))
        
        fig.update_layout(
            title="Interactive Data Hierarchy - Click on nodes to see details",
            showlegend=False,
            hovermode='closest',
            margin=dict(b=20,l=5,r=5,t=40),
            annotations=[
                dict(
                    text="Click on any node to see detailed information below",
                    showarrow=False,
                    xref="paper", yref="paper",
                    x=0.005, y=-0.002,
                    xanchor='left', yanchor='bottom',
                    font=dict(color='gray', size=12)
                )
            ],
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            plot_bgcolor='white'
        )
        
        return fig, node_info
    
    def setup_layout(self):
        """Setup the Dash app layout."""
        fig, self.node_info = self.create_hierarchy_tree()
        
        self.app.layout = html.Div([
            html.H1("Interactive Data Visualizer", 
                   style={'textAlign': 'center', 'color': '#2C3E50', 'marginBottom': 30}),
            
            dcc.Graph(
                id='hierarchy-graph',
                figure=fig,
                style={'height': '500px'}
            ),
            
            html.Div(id='detail-panel', 
                    style={'marginTop': 20, 'padding': 20, 'backgroundColor': '#F8F9FA', 
                           'borderRadius': 10, 'border': '1px solid #DEE2E6'}),
            
            html.Div([
                html.H3("Data Summary", style={'color': '#2C3E50'}),
                html.Div(id='summary-stats')
            ], style={'marginTop': 30, 'padding': 20, 'backgroundColor': '#E8F4FD', 
                     'borderRadius': 10, 'border': '1px solid #BEE5EB'})
        ], style={'padding': 20, 'fontFamily': 'Arial, sans-serif'})
    
    def setup_callbacks(self):
        """Setup Dash callbacks for interactivity."""
        @self.app.callback(
            [Output('detail-panel', 'children'),
             Output('summary-stats', 'children')],
            [Input('hierarchy-graph', 'clickData')]
        )
        def update_details(clickData):
            if not clickData:
                return self.get_default_details(), self.get_summary_stats()
            
            # Get clicked node
            point = clickData['points'][0]
            node_id = point['customdata']
            node_data = self.node_info.get(node_id, {})
            
            return self.format_node_details(node_id, node_data), self.get_summary_stats()
    
    def get_default_details(self):
        """Return default detail panel content."""
        return html.Div([
            html.H3("Welcome to Interactive Data Visualizer", style={'color': '#2C3E50'}),
            html.P("Click on any node in the graph above to see detailed information about that element."),
            html.P("The visualization shows the hierarchical relationship between Users, Appointments, and Workflows."),
            html.Ul([
                html.Li("🔴 Red nodes: Users"),
                html.Li("🟢 Teal nodes: Appointments"),
                html.Li("🔵 Blue nodes: Workflows")
            ])
        ])
    
    def format_node_details(self, node_id, node_data):
        """Format detailed information for a clicked node."""
        if node_id.startswith('user_'):
            return self.format_user_details(node_data)
        elif node_id.startswith('appt_'):
            return self.format_appointment_details(node_data)
        elif node_id.startswith('wf_'):
            return self.format_workflow_details(node_data)
        else:
            return html.Div("No details available")
    
    def format_user_details(self, user_data):
        """Format user details."""
        return html.Div([
            html.H3(f"👤 User Details", style={'color': '#E74C3C'}),
            html.Table([
                html.Tr([html.Td(html.B(k)), html.Td(str(v))])
                for k, v in user_data.items() if v is not None
            ], style={'width': '100%', 'borderCollapse': 'collapse'}),
        ])

    def format_appointment_details(self, appt_data):
        """Format appointment details."""
        appt = appt_data.get('Appointment', {})
        support_data = appt_data.get('Appointments_Support', [])
        outbound_data = appt_data.get('OutBound_Document', [])
        workflows = appt_data.get('Workflows', [])

        return html.Div([
            html.H3(f"📅 Appointment Details", style={'color': '#17A2B8'}),

            # Main appointment info
            html.H4("Main Information"),
            html.Table([
                html.Tr([html.Td(html.B(k)), html.Td(str(v))])
                for k, v in appt.items() if v is not None
            ], style={'width': '100%', 'borderCollapse': 'collapse', 'marginBottom': 20}),

            # Support data
            html.H4(f"Appointments Support ({len(support_data)} records)"),
            html.Div([
                html.Details([
                    html.Summary(f"Support Record {i+1}"),
                    html.Table([
                        html.Tr([html.Td(html.B(k)), html.Td(str(v))])
                        for k, v in support.items() if v is not None
                    ], style={'width': '100%', 'marginLeft': 20})
                ]) for i, support in enumerate(support_data)
            ]),

            # Outbound documents
            html.H4(f"OutBound Documents ({len(outbound_data)} records)"),
            html.Div([
                html.Details([
                    html.Summary(f"Document {i+1}"),
                    html.Table([
                        html.Tr([html.Td(html.B(k)), html.Td(str(v))])
                        for k, v in doc.items() if v is not None
                    ], style={'width': '100%', 'marginLeft': 20})
                ]) for i, doc in enumerate(outbound_data)
            ]),

            # Workflows summary
            html.H4(f"Associated Workflows ({len(workflows)} workflows)"),
            html.Ul([
                html.Li(f"Workflow {wf.get('AppointmentId', 'N/A')} - "
                       f"{len(wf.get('WorkflowAuditLog', []))} audits, "
                       f"{len(wf.get('KioskWorkflow', []))} kiosks")
                for wf in workflows
            ])
        ])

    def format_workflow_details(self, workflow_data):
        """Format workflow details."""
        appt_id = workflow_data.get('AppointmentId', 'N/A')
        audit_logs = workflow_data.get('WorkflowAuditLog', [])
        kiosk_workflows = workflow_data.get('KioskWorkflow', [])

        return html.Div([
            html.H3(f"⚙️ Workflow Details (AppointmentId: {appt_id})", style={'color': '#6F42C1'}),

            # Audit logs
            html.H4(f"Workflow Audit Logs ({len(audit_logs)} records)"),
            html.Div([
                html.Details([
                    html.Summary(f"Audit Log {i+1} - {audit.get('Action', 'N/A')} "
                               f"({self.format_datetime(audit.get('ActionDateTime'))})"),
                    html.Table([
                        html.Tr([html.Td(html.B(k)), html.Td(str(v))])
                        for k, v in audit.items() if v is not None
                    ], style={'width': '100%', 'marginLeft': 20})
                ]) for i, audit in enumerate(audit_logs)
            ]),

            # Kiosk workflows
            html.H4(f"Kiosk Workflows ({len(kiosk_workflows)} records)"),
            html.Div([
                html.Details([
                    html.Summary(f"Kiosk {kiosk.get('KioskWorkflowId', 'N/A')} - "
                               f"{kiosk.get('WorkflowName', 'N/A')}"),
                    html.Table([
                        html.Tr([html.Td(html.B(k)), html.Td(str(v))])
                        for k, v in kiosk.items() if v is not None
                    ], style={'width': '100%', 'marginLeft': 20})
                ]) for kiosk in kiosk_workflows
            ])
        ])

    def get_summary_stats(self):
        """Get summary statistics."""
        appointments = self.data.get('Appointments', [])
        n_appt = len(appointments)
        n_sup = sum(len(a.get('Appointments_Support', [])) for a in appointments)
        n_outbound = sum(len(a.get('OutBound_Document', [])) for a in appointments)
        n_wf = sum(len(a.get('Workflows', [])) for a in appointments)
        n_audit = sum(sum(len(wf.get('WorkflowAuditLog', [])) for wf in a.get('Workflows', [])) for a in appointments)
        n_kiosk = sum(sum(len(wf.get('KioskWorkflow', [])) for wf in a.get('Workflows', [])) for a in appointments)

        return html.Div([
            html.Div([
                html.Div([
                    html.H4(str(n_appt), style={'margin': 0, 'color': '#17A2B8'}),
                    html.P("Appointments", style={'margin': 0})
                ], style={'textAlign': 'center', 'padding': 10, 'backgroundColor': 'white',
                         'borderRadius': 5, 'margin': 5, 'flex': 1}),

                html.Div([
                    html.H4(str(n_sup), style={'margin': 0, 'color': '#28A745'}),
                    html.P("Support Records", style={'margin': 0})
                ], style={'textAlign': 'center', 'padding': 10, 'backgroundColor': 'white',
                         'borderRadius': 5, 'margin': 5, 'flex': 1}),

                html.Div([
                    html.H4(str(n_outbound), style={'margin': 0, 'color': '#FFC107'}),
                    html.P("OutBound Docs", style={'margin': 0})
                ], style={'textAlign': 'center', 'padding': 10, 'backgroundColor': 'white',
                         'borderRadius': 5, 'margin': 5, 'flex': 1}),

                html.Div([
                    html.H4(str(n_wf), style={'margin': 0, 'color': '#6F42C1'}),
                    html.P("Workflows", style={'margin': 0})
                ], style={'textAlign': 'center', 'padding': 10, 'backgroundColor': 'white',
                         'borderRadius': 5, 'margin': 5, 'flex': 1}),

                html.Div([
                    html.H4(str(n_audit), style={'margin': 0, 'color': '#DC3545'}),
                    html.P("Audit Logs", style={'margin': 0})
                ], style={'textAlign': 'center', 'padding': 10, 'backgroundColor': 'white',
                         'borderRadius': 5, 'margin': 5, 'flex': 1}),

                html.Div([
                    html.H4(str(n_kiosk), style={'margin': 0, 'color': '#FD7E14'}),
                    html.P("Kiosk Records", style={'margin': 0})
                ], style={'textAlign': 'center', 'padding': 10, 'backgroundColor': 'white',
                         'borderRadius': 5, 'margin': 5, 'flex': 1})

            ], style={'display': 'flex', 'flexWrap': 'wrap'})
        ])

    def run(self, debug=True, port=8050):
        """Run the Dash app."""
        print(f"🚀 Starting Interactive Data Visualizer...")
        print(f"📊 Data loaded: {len(self.data.get('Appointments', []))} appointments")
        print(f"🌐 Open your browser to: http://localhost:{port}")
        self.app.run(debug=debug, port=port)


def main():
    """Main function to run the visualizer."""
    if len(sys.argv) != 2:
        print("Usage: python interactive_data_visualizer.py <json_file_path>")
        print("Example: python interactive_data_visualizer.py Andrew_Jennings.json")
        sys.exit(1)

    json_file = sys.argv[1]

    try:
        visualizer = InteractiveDataVisualizer(json_file)
        visualizer.run()
    except FileNotFoundError:
        print(f"❌ Error: File '{json_file}' not found.")
        print("Make sure to run testdb.py first to generate the JSON file.")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
