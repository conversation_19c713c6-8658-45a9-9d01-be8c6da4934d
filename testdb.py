"""
trace_user_to_json_print.py
Walks: Users → Appointments → AppointmentSupport → WorkflowAudit → KioskWorkflow
Usage:
    python trace_user_to_json_print.py "Andrew" "Jennings"
"""

import json
import sys
from datetime import datetime, timezone

import pandas as pd
import pyodbc


# ────────────── 0.  CONNECT ──────────────
conn = pyodbc.connect(
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=HAARLT0397;"
    "DATABASE=V811QA;"
    "Trusted_Connection=yes;"
    "Encrypt=yes;"
    "TrustServerCertificate=yes;"
)


# ────────────── 1.  HELPERS ──────────────
def fetch_df(cur, sql: str, params=()):
    cur.execute(sql, params)
    cols = [c[0] for c in cur.description]
    return pd.DataFrame.from_records(cur.fetchall(), columns=cols)


def build_user_json(first: str, last: str) -> dict:
    with conn.cursor() as cur:
        # 1-a.  User
        user_df = fetch_df(
            cur,
            "SELECT * FROM Users WHERE Firstname = ? AND Lastname = ?",
            (first, last),
        )
        if user_df.empty:
            raise ValueError(f"No user found for {first} {last}")

        user    = user_df.iloc[0].to_dict()
        user_id = user["UserID"]

        # 1-b.  Appointments
        appt_df = fetch_df(
            cur,
            "SELECT * FROM Appointments WHERE UserID = ?",
            (user_id,),
        )

        # 1-c.  Build hierarchical structure: User -> Appointments -> Associated Data
        appointments = []
        for _, appt_row in appt_df.iterrows():
            appt_rec = appt_row.to_dict()
            appt_no  = appt_rec["AppNo"]

            # Get all associated data for this appointment
            # Appointments_Support rows for this appointment
            sup_df = fetch_df(
                cur,
                "SELECT * FROM Appointments_Support WHERE AppointmentNo = ?",
                (appt_no,),
            )

            # OutBound_Document rows for this appointment
            outbound_df = fetch_df(
                cur,
                "SELECT * FROM OutBound_Document WHERE transactionNo = ?",
                (appt_no,),
            )

            # Build support data with their associated workflows
            support_records = []
            for _, sup_row in sup_df.iterrows():
                support_rec = sup_row.to_dict()
                support_appt_id = support_rec["AppointmentId"]

                # WorkflowAuditLog rows for this AppointmentId
                audit_df = fetch_df(
                    cur,
                    "SELECT * FROM WorkflowAuditLog WHERE AppointmentId = ?",
                    (support_appt_id,),
                )

                # KioskWorkflow rows referenced by those audits
                kiosk_ids = audit_df["KioskWorkflowId"].dropna().unique().tolist()
                if kiosk_ids:
                    placeholders = ", ".join("?" * len(kiosk_ids))
                    kiosk_df = fetch_df(
                        cur,
                        f"SELECT * FROM KioskWorkflow WHERE KioskWorkflowId IN ({placeholders})",
                        kiosk_ids,
                    )
                else:
                    kiosk_df = pd.DataFrame()

                # Attach workflow data to this support record
                support_rec["WorkflowData"] = {
                    "WorkflowAuditLog": audit_df.to_dict("records"),
                    "KioskWorkflow": kiosk_df.to_dict("records"),
                }
                support_records.append(support_rec)

            # Create the hierarchical appointment structure
            appointment_data = {
                "AppointmentDetails": appt_rec,
                "AssociatedData": {
                    "SupportRecords": support_records,
                    "OutboundDocuments": outbound_df.to_dict("records"),
                },
                "Summary": {
                    "TotalSupportRecords": len(support_records),
                    "TotalOutboundDocuments": len(outbound_df),
                    "TotalWorkflowAudits": sum(len(sr.get("WorkflowData", {}).get("WorkflowAuditLog", [])) for sr in support_records),
                    "TotalKioskWorkflows": sum(len(sr.get("WorkflowData", {}).get("KioskWorkflow", [])) for sr in support_records),
                }
            }

            appointments.append(appointment_data)

        # Calculate user-level summary
        user_summary = {
            "TotalAppointments": len(appointments),
            "TotalSupportRecords": sum(a["Summary"]["TotalSupportRecords"] for a in appointments),
            "TotalOutboundDocuments": sum(a["Summary"]["TotalOutboundDocuments"] for a in appointments),
            "TotalWorkflowAudits": sum(a["Summary"]["TotalWorkflowAudits"] for a in appointments),
            "TotalKioskWorkflows": sum(a["Summary"]["TotalKioskWorkflows"] for a in appointments),
        }

        return {
            "ExtractedAt": datetime.now(timezone.utc).isoformat(timespec="seconds").replace("+00:00", "Z"),
            "User": {
                "UserDetails": user,
                "Summary": user_summary,
                "Appointments": appointments,
            }
        }


# ────────────── 2.  CLI ENTRY ──────────────
if __name__ == "__main__":
    if len(sys.argv) != 3:
        sys.exit("Usage: python trace_user_to_json_print.py <FirstName> <LastName>")

    try:
        payload = build_user_json(sys.argv[1], sys.argv[2])
    except Exception as exc:
        sys.exit(f"❌  {exc}")

    # ---------- 3.  HIERARCHY PRINTER (ENHANCED) ----------
    import sys
    from datetime import datetime

    # ANSI color codes
    class C:
        HEADER = '\033[95m'
        OKBLUE = '\033[94m'
        OKCYAN = '\033[96m'
        OKGREEN = '\033[92m'
        WARNING = '\033[93m'
        FAIL = '\033[91m'
        ENDC = '\033[0m'
        BOLD = '\033[1m'
        UNDERLINE = '\033[4m'

    def fmt_dt(val):
        """Try to turn whatever date/time field we find into `DD-Mon-YYYY HH:MM`."""
        if isinstance(val, datetime):
            return val.strftime("%d-%b-%Y %H:%M")
        try:
            return datetime.fromisoformat(str(val)).strftime("%d-%b-%Y %H:%M")
        except Exception:
            return str(val)

    # --- Summary from new hierarchical structure ---
    user_data = payload["User"]
    user_summary = user_data["Summary"]
    appointments = user_data["Appointments"]

    print(f"\n{C.BOLD}{C.OKCYAN}=== USER DATA SUMMARY ==={C.ENDC}")
    print(f"{C.OKGREEN}Appointments: {user_summary['TotalAppointments']}{C.ENDC}  |  {C.OKBLUE}Support Records: {user_summary['TotalSupportRecords']}{C.ENDC}  |  {C.WARNING}Workflow Audits: {user_summary['TotalWorkflowAudits']}{C.ENDC}  |  {C.FAIL}Kiosk Workflows: {user_summary['TotalKioskWorkflows']}{C.ENDC}  |  {C.OKCYAN}Outbound Documents: {user_summary['TotalOutboundDocuments']}{C.ENDC}\n")

    print(f"{C.BOLD}{C.OKCYAN}=== HIERARCHICAL APPOINTMENT MAP ==={C.ENDC}")
    user_details = user_data["UserDetails"]
    user_name = f"{user_details.get('FirstName', '')} {user_details.get('LastName', '')}"
    print(f"{C.BOLD}{C.HEADER}👤 User: {user_name} (ID: {user_details.get('UserID')}){C.ENDC}")

    for i, appointment in enumerate(appointments):
        appt_details = appointment["AppointmentDetails"]
        appt_summary = appointment["Summary"]
        associated_data = appointment["AssociatedData"]

        appt_id = appt_details.get("AppointmentId") or appt_details.get("AppId") or appt_details.get("ID")
        appt_no = appt_details.get("AppointmentNo") or appt_details.get("AppNo")
        dt_val = (
            appt_details.get("AppointmentDateTime")
            or appt_details.get("ApptDateTime")
            or appt_details.get("AppDate")
            or appt_details.get("StartTime")
            or appt_details.get("DateTime")
            or ""
        )

        # Appointment header
        prefix = "├─" if i < len(appointments) - 1 else "└─"
        print(f"{prefix} {C.BOLD}{C.OKGREEN}📅 Appointment {appt_id}{C.ENDC} (AppNo: {appt_no}, {fmt_dt(dt_val)})")

        # Summary line
        indent = "│   " if i < len(appointments) - 1 else "    "
        print(f"{indent}├─ {C.BOLD}Summary:{C.ENDC} {appt_summary['TotalSupportRecords']} Support Records, {appt_summary['TotalOutboundDocuments']} Outbound Docs, {appt_summary['TotalWorkflowAudits']} Audits, {appt_summary['TotalKioskWorkflows']} Kiosk Workflows")

        # Support Records with their workflows
        support_records = associated_data["SupportRecords"]
        print(f"{indent}├─ {C.OKBLUE}📋 Support Records ({len(support_records)}){C.ENDC}")
        for j, support in enumerate(support_records[:3]):  # Show first 3
            support_prefix = f"{indent}│   ├─" if j < min(len(support_records), 3) - 1 else f"{indent}│   └─"
            support_id = support.get("AppointmentId", "N/A")
            workflow_data = support.get("WorkflowData", {})
            n_audits = len(workflow_data.get("WorkflowAuditLog", []))
            n_kiosks = len(workflow_data.get("KioskWorkflow", []))

            print(f"{support_prefix} Support ID: {support_id} → {n_audits} Audits, {n_kiosks} Kiosk Workflows")

            # Show workflow details if present
            if n_audits > 0 or n_kiosks > 0:
                workflow_indent = f"{indent}│   │   " if j < min(len(support_records), 3) - 1 else f"{indent}    │   "
                if n_audits > 0:
                    print(f"{workflow_indent}├─ {C.WARNING}⚙️ {n_audits} Workflow Audit{'s' if n_audits != 1 else ''}{C.ENDC}")
                if n_kiosks > 0:
                    kiosk_ids = [kw.get('KioskWorkflowId') for kw in workflow_data.get("KioskWorkflow", [])]
                    print(f"{workflow_indent}└─ {C.FAIL}🖥️ {n_kiosks} Kiosk Workflow{'s' if n_kiosks != 1 else ''}{C.ENDC} (IDs: {', '.join(map(str, kiosk_ids))})")

        if len(support_records) > 3:
            print(f"{indent}│       ... ({len(support_records) - 3} more support records)")

        # Outbound Documents
        outbound_docs = associated_data["OutboundDocuments"]
        doc_prefix = f"{indent}└─" if len(outbound_docs) > 0 else f"{indent}└─"
        print(f"{doc_prefix} {C.OKCYAN}📄 Outbound Documents ({len(outbound_docs)}){C.ENDC}")
        for k, doc in enumerate(outbound_docs[:2]):  # Show first 2
            doc_preview = ', '.join(f"{key}={val}" for key, val in list(doc.items())[:2])
            final_indent = f"{indent}    " if len(outbound_docs) > 0 else f"{indent}    "
            print(f"{final_indent}• {doc_preview}{' ...' if len(doc) > 2 else ''}")
        if len(outbound_docs) > 2:
            print(f"{indent}    ... ({len(outbound_docs) - 2} more documents)")

        print()  # blank line between appointments

    # ---------- 4.  FULL JSON (UNCHANGED) ----------
    # Pretty-print the entire payload (comment out if not needed)
    # print(json.dumps(payload, indent=2, default=str))

    # ---------- 5.  OPTIONAL WRITE TO FILE ----------
    outfile = f"{sys.argv[1]}_{sys.argv[2]}.json".replace(" ", "_")
    with open(outfile, "w", encoding="utf-8") as f:
        json.dump(payload, f, indent=2, default=str)
    print(f"\n✅  Wrote full JSON to {outfile}")
