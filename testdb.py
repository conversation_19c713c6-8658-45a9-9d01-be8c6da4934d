"""
trace_user_to_json_print.py
Walks: Users → Appointments → AppointmentSupport → WorkflowAudit → KioskWorkflow
Usage:
    python trace_user_to_json_print.py "Andrew" "Jennings"
"""

import json
import sys
from datetime import datetime

import pandas as pd
import pyodbc


# ────────────── 0.  CONNECT ──────────────
conn = pyodbc.connect(
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=HAARLT0397;"
    "DATABASE=V811QA;"
    "Trusted_Connection=yes;"
    "Encrypt=yes;"
    "TrustServerCertificate=yes;"
)


# ────────────── 1.  HELPERS ──────────────
def fetch_df(cur, sql: str, params=()):
    cur.execute(sql, params)
    cols = [c[0] for c in cur.description]
    return pd.DataFrame.from_records(cur.fetchall(), columns=cols)


def build_user_json(first: str, last: str) -> dict:
    with conn.cursor() as cur:
        # 1-a.  User
        user_df = fetch_df(
            cur,
            "SELECT * FROM Users WHERE Firstname = ? AND Lastname = ?",
            (first, last),
        )
        if user_df.empty:
            raise ValueError(f"No user found for {first} {last}")

        user    = user_df.iloc[0].to_dict()
        user_id = user["UserID"]

        # 1-b.  Appointments
        appt_df = fetch_df(
            cur,
            "SELECT * FROM Appointments WHERE UserID = ?",
            (user_id,),
        )

        # 1-c.  Chain: Appointments_Support → WorkflowAuditLog → KioskWorkflow
        appointments = []
        for _, appt_row in appt_df.iterrows():
            appt_rec = appt_row.to_dict()
            appt_no  = appt_rec["AppNo"]

            # Appointments_Support rows for this appointment
            sup_df = fetch_df(
                cur,
                "SELECT * FROM Appointments_Support WHERE AppointmentNo = ?",
                (appt_no,),
            )

            # OutBound_Document rows for this appointment
            outbound_df = fetch_df(
                cur,
                "SELECT * FROM OutBound_Document WHERE transactionNo = ?",
                (appt_no,),
            )

            workflows = []
            for _, sup_row in sup_df.iterrows():
                appt_id = sup_row["AppointmentId"]

                # WorkflowAuditLog rows for this AppointmentId
                audit_df = fetch_df(
                    cur,
                    "SELECT * FROM WorkflowAuditLog WHERE AppointmentId = ?",
                    (appt_id,),
                )

                # KioskWorkflow rows referenced by those audits
                kiosk_ids = audit_df["KioskWorkflowId"].dropna().unique().tolist()
                if kiosk_ids:
                    placeholders = ", ".join("?" * len(kiosk_ids))
                    kiosk_df = fetch_df(
                        cur,
                        f"SELECT * FROM KioskWorkflow WHERE KioskWorkflowId IN ({placeholders})",
                        kiosk_ids,
                    )
                else:
                    kiosk_df = pd.DataFrame()

                workflows.append(
                    {
                        "AppointmentId": appt_id,
                        "WorkflowAuditLog": audit_df.to_dict("records"),
                        "KioskWorkflow": kiosk_df.to_dict("records"),
                    }
                )

            appointments.append(
                {
                    "Appointment": appt_rec,
                    "Appointments_Support": sup_df.to_dict("records"),
                    "OutBound_Document": outbound_df.to_dict("records"),
                    "Workflows": workflows,
                }
            )

        return {
            "ExtractedAt": datetime.utcnow().isoformat(timespec="seconds") + "Z",
            "User": user,
            "Appointments": appointments,
        }


# ────────────── 2.  CLI ENTRY ──────────────
if __name__ == "__main__":
    if len(sys.argv) != 3:
        sys.exit("Usage: python trace_user_to_json_print.py <FirstName> <LastName>")

    try:
        payload = build_user_json(sys.argv[1], sys.argv[2])
    except Exception as exc:
        sys.exit(f"❌  {exc}")

    # ---------- 3.  HIERARCHY PRINTER (ENHANCED) ----------
    import sys
    from datetime import datetime

    # ANSI color codes
    class C:
        HEADER = '\033[95m'
        OKBLUE = '\033[94m'
        OKCYAN = '\033[96m'
        OKGREEN = '\033[92m'
        WARNING = '\033[93m'
        FAIL = '\033[91m'
        ENDC = '\033[0m'
        BOLD = '\033[1m'
        UNDERLINE = '\033[4m'

    def fmt_dt(val):
        """Try to turn whatever date/time field we find into `DD-Mon-YYYY HH:MM`."""
        if isinstance(val, datetime):
            return val.strftime("%d-%b-%Y %H:%M")
        try:
            return datetime.fromisoformat(str(val)).strftime("%d-%b-%Y %H:%M")
        except Exception:
            return str(val)

    # --- Summary ---
    n_appt = len(payload["Appointments"])
    n_sup = sum(len(a["Appointments_Support"]) for a in payload["Appointments"])
    n_wf = sum(len(a["Workflows"]) for a in payload["Appointments"])
    n_audit = sum(sum(len(wf["WorkflowAuditLog"]) for wf in a["Workflows"]) for a in payload["Appointments"])
    n_kiosk = sum(sum(len(wf["KioskWorkflow"]) for wf in a["Workflows"]) for a in payload["Appointments"])
    n_outbound = sum(len(a["OutBound_Document"]) for a in payload["Appointments"])

    print(f"\n{C.BOLD}{C.OKCYAN}=== APPOINTMENT MAP SUMMARY ==={C.ENDC}")
    print(f"{C.OKGREEN}Appointments: {n_appt}{C.ENDC}  |  {C.OKBLUE}Supports: {n_sup}{C.ENDC}  |  {C.HEADER}Workflows: {n_wf}{C.ENDC}  |  {C.WARNING}Audits: {n_audit}{C.ENDC}  |  {C.FAIL}Kiosks: {n_kiosk}{C.ENDC}  |  {C.OKCYAN}OutBound_Documents: {n_outbound}{C.ENDC}\n")

    print(f"{C.BOLD}{C.OKCYAN}=== APPOINTMENT MAP ==={C.ENDC}")
    for a in payload["Appointments"]:
        appt = a["Appointment"]
        appt_id  = appt.get("AppointmentId") or appt.get("AppId") or appt.get("ID")
        appt_no  = appt.get("AppointmentNo") or appt.get("AppNo")
        dt_val = (
            appt.get("AppointmentDateTime")
            or appt.get("ApptDateTime")
            or appt.get("StartTime")
            or appt.get("DateTime")
            or ""
        )
        print(f"{C.BOLD}├─ {C.OKGREEN}Appointment {appt_id}{C.ENDC}  (AppNo {appt_no}, {fmt_dt(dt_val)})")

        # Support rows preview
        n_sup = len(a["Appointments_Support"])
        print(f"│   ├─ {C.OKBLUE}{n_sup} Appointments_Support row{'s' if n_sup != 1 else ''}{C.ENDC}")
        for i, sup in enumerate(a["Appointments_Support"][:2]):
            preview = ', '.join(f"{k}={v}" for k, v in list(sup.items())[:2])
            print(f"│   │   • {preview}{' ...' if len(sup)>2 else ''}")
        if n_sup > 2:
            print(f"│   │   ... ({n_sup-2} more)")

        # OutBound_Document preview
        n_outbound = len(a["OutBound_Document"])
        print(f"│   ├─ {C.OKCYAN}{n_outbound} OutBound_Document row{'s' if n_outbound != 1 else ''}{C.ENDC}")
        for i, ob in enumerate(a["OutBound_Document"][:2]):
            preview = ', '.join(f"{k}={v}" for k, v in list(ob.items())[:2])
            print(f"│   │   • {preview}{' ...' if len(ob)>2 else ''}")
        if n_outbound > 2:
            print(f"│   │   ... ({n_outbound-2} more)")

        # Workflows
        for j, wf in enumerate(a["Workflows"]):
            wf_id  = wf['AppointmentId']
            wf_prefix = "│   ├─" if j < len(a["Workflows"]) - 1 else "│   └─"
            print(f"{wf_prefix} {C.HEADER}Workflow {wf_id}{C.ENDC}")

            # Audit rows preview
            n_audit = len(wf["WorkflowAuditLog"])
            print(f"│   │   ├─ {C.WARNING}{n_audit} WorkflowAuditLog row{'s' if n_audit != 1 else ''}{C.ENDC}")
            for k, audit in enumerate(wf["WorkflowAuditLog"][:2]):
                preview = ', '.join(f"{k}={v}" for k, v in list(audit.items())[:2])
                print(f"│   │   │   • {preview}{' ...' if len(audit)>2 else ''}")
            if n_audit > 2:
                print(f"│   │   │   ... ({n_audit-2} more)")

            # Kiosk rows + IDs
            kiosk_rows = wf["KioskWorkflow"]
            kiosk_ids  = [kw.get('KioskWorkflowId') for kw in kiosk_rows]
            print(f"│   │   └─ {C.FAIL}{len(kiosk_rows)} KioskWorkflow row{'s' if len(kiosk_rows) != 1 else ''}{C.ENDC}" +
                  (f"   (IDs {', '.join(map(str, kiosk_ids))})" if kiosk_ids else ""))
        print()  # blank line between appointments

    # ---------- 4.  FULL JSON (UNCHANGED) ----------
    # Pretty-print the entire payload (comment out if not needed)
    # print(json.dumps(payload, indent=2, default=str))

    # ---------- 5.  OPTIONAL WRITE TO FILE ----------
    outfile = f"{sys.argv[1]}_{sys.argv[2]}.json".replace(" ", "_")
    with open(outfile, "w", encoding="utf-8") as f:
        json.dump(payload, f, indent=2, default=str)
    print(f"\n✅  Wrote full JSON to {outfile}")
